import sys
import json
import asyncio
import os
from typing import Any, Dict, Optional, Tuple
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import httpx
from fastmcp import FastMCP

# 配置类
class ServerConfig:
    """服务器配置管理"""

    def __init__(self):
        self.cache_duration_minutes = int(os.getenv('FASTMCP_CACHE_DURATION', '30'))
        self.http_timeout = float(os.getenv('FASTMCP_HTTP_TIMEOUT', '30.0'))
        self.user_agent = os.getenv('FASTMCP_USER_AGENT', 'URL-JSON-MCP/2.0 (httpx)')
        self.log_level = os.getenv('FASTMCP_LOG_LEVEL', 'INFO')
        self.mask_error_details = os.getenv('FASTMCP_MASK_ERROR_DETAILS', 'false').lower() == 'true'

# 全局配置实例
config = ServerConfig()

# 自定义序列化器
def custom_serializer(data):
    """自定义数据序列化器，支持更好的JSON格式化"""
    if isinstance(data, dict):
        return json.dumps(data, ensure_ascii=False, indent=2, sort_keys=True)
    elif isinstance(data, (list, tuple)):
        return json.dumps(data, ensure_ascii=False, indent=2)
    else:
        return json.dumps(data, ensure_ascii=False, indent=2)

# Redirect all debug prints to stderr
def debug_print(*args, **kwargs):
    if config.log_level in ['DEBUG', 'INFO']:
        print(*args, file=sys.stderr, **kwargs)


class AsyncURLContentManager:
    """异步URL内容获取和缓存管理器"""

    def __init__(self, cache_duration_minutes: int = 30):
        """
        初始化异步URL内容管理器

        Args:
            cache_duration_minutes: 缓存持续时间（分钟）
        """
        self.cache: Dict[str, Tuple[datetime, Any]] = {}
        self.cache_duration = timedelta(minutes=cache_duration_minutes)
        self._client: Optional[httpx.AsyncClient] = None
        self._timeout = float(os.getenv('FASTMCP_HTTP_TIMEOUT', '30.0'))
        self._user_agent = os.getenv('FASTMCP_USER_AGENT', 'URL-JSON-MCP/2.0 (httpx)')
        debug_print(f"AsyncURLContentManager initialized with {cache_duration_minutes}min cache duration")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_client()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def _ensure_client(self):
        """确保HTTP客户端已初始化"""
        if self._client is None or self._client.is_closed:
            self._client = httpx.AsyncClient(
                timeout=self._timeout,
                headers={'User-Agent': self._user_agent},
                limits=httpx.Limits(max_keepalive_connections=10, max_connections=20)
            )
            debug_print("AsyncClient initialized")

    async def close(self):
        """关闭HTTP客户端"""
        if self._client and not self._client.is_closed:
            await self._client.aclose()
            debug_print("AsyncClient closed")

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False

        cached_time, _ = self.cache[cache_key]
        return datetime.now() - cached_time < self.cache_duration

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取内容"""
        if self._is_cache_valid(cache_key):
            _, content = self.cache[cache_key]
            debug_print(f"Cache hit for key: {cache_key}")
            return content
        return None

    def _store_in_cache(self, cache_key: str, content: Any) -> None:
        """存储内容到缓存"""
        self.cache[cache_key] = (datetime.now(), content)
        debug_print(f"Cached content for key: {cache_key}")

    def _cleanup_expired_cache(self) -> int:
        """清理过期的缓存条目"""
        now = datetime.now()
        expired_keys = [
            key for key, (cached_time, _) in self.cache.items()
            if now - cached_time >= self.cache_duration
        ]

        for key in expired_keys:
            del self.cache[key]

        if expired_keys:
            debug_print(f"Cleaned up {len(expired_keys)} expired cache entries")

        return len(expired_keys)

    async def _try_http_request(self, url: str, method: str) -> Dict[str, Any]:
        """
        异步尝试使用指定HTTP方法获取URL内容

        Args:
            url: 要获取的URL
            method: HTTP方法 ('GET' 或 'POST')

        Returns:
            包含请求结果的字典
        """
        return await self._execute_http_request(url, method, response_type='json')

    async def _execute_http_request(self, url: str, method: str, response_type: str = 'json') -> Dict[str, Any]:
        """
        异步统一的HTTP请求执行方法

        Args:
            url: 要获取的URL
            method: HTTP方法 ('GET' 或 'POST')
            response_type: 响应类型 ('json' 或 'text')

        Returns:
            包含请求结果的字典
        """
        try:
            await self._ensure_client()
            debug_print(f"Trying async {method} request for {response_type} content to: {url}")

            if method.upper() == 'GET':
                response = await self._client.get(url)
            elif method.upper() == 'POST':
                response = await self._client.post(url)  # 无请求体的POST
            else:
                return {
                    "success": False,
                    "error": f"Unsupported HTTP method: {method}",
                    "url": url
                }

            response.raise_for_status()  # 抛出HTTP错误

            # 根据响应类型处理内容
            try:
                if response_type == 'json':
                    content = response.json()
                    debug_print(f"Successfully parsed JSON from {url} using {method}")
                    return {
                        "success": True,
                        "data": content,
                        "method": method.upper(),
                        "status_code": response.status_code
                    }
                elif response_type == 'text':
                    content = response.text
                    debug_print(f"Successfully retrieved text content from {url} using {method}")
                    return {
                        "success": True,
                        "content": content,
                        "method": method.upper(),
                        "status_code": response.status_code
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Unsupported response type: {response_type}",
                        "method": method.upper(),
                        "url": url,
                        "status_code": response.status_code
                    }

            except json.JSONDecodeError as e:
                debug_print(f"JSON decode error for {url} using {method}: {e}")
                return {
                    "success": False,
                    "error": f"Invalid JSON response: {str(e)}",
                    "method": method.upper(),
                    "url": url,
                    "status_code": response.status_code
                }
            except Exception as e:
                debug_print(f"Content processing error for {url} using {method}: {e}")
                return {
                    "success": False,
                    "error": f"Failed to process {response_type} content: {str(e)}",
                    "method": method.upper(),
                    "url": url,
                    "status_code": response.status_code
                }

        except httpx.HTTPStatusError as e:
            debug_print(f"HTTP error for {url} using {method}: {e}")
            return {
                "success": False,
                "error": f"HTTP {e.response.status_code}: {e.response.reason_phrase}",
                "method": method.upper(),
                "url": url,
                "status_code": e.response.status_code,
                "error_type": "http_status"
            }

        except httpx.TimeoutException as e:
            debug_print(f"Timeout error for {url} using {method}: {e}")
            return {
                "success": False,
                "error": f"Request timeout: {str(e)}",
                "method": method.upper(),
                "url": url,
                "error_type": "timeout"
            }

        except httpx.RequestError as e:
            debug_print(f"Request error for {url} using {method}: {e}")
            return {
                "success": False,
                "error": f"Network error: {str(e)}",
                "method": method.upper(),
                "url": url,
                "error_type": "network"
            }

        except Exception as e:
            debug_print(f"Unexpected error for {url} using {method}: {e}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "method": method.upper(),
                "url": url,
                "error_type": "unexpected"
            }

    async def _try_http_request_text(self, url: str, method: str) -> Dict[str, Any]:
        """
        异步尝试使用指定HTTP方法获取URL文本内容

        Args:
            url: 要获取的URL
            method: HTTP方法 ('GET' 或 'POST')

        Returns:
            包含请求结果的字典
        """
        return await self._execute_http_request(url, method, response_type='text')

    async def fetch_json(self, url: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        异步从URL获取JSON内容，先尝试GET请求，失败后尝试POST请求

        Args:
            url: 要获取的URL
            force_refresh: 是否强制刷新缓存

        Returns:
            包含状态和数据的字典
        """
        debug_print(f"Fetching JSON from URL: {url}, force_refresh: {force_refresh}")

        # 清理过期缓存
        self._cleanup_expired_cache()

        # 检查缓存（除非强制刷新）
        if not force_refresh:
            cached_content = self._get_from_cache(url)
            if cached_content is not None:
                return {
                    "success": True,
                    "data": cached_content,
                    "source": "cache",
                    "url": url
                }

        # 尝试HTTP请求：先GET，后POST
        methods_to_try = ['GET', 'POST']
        last_error = None

        for method in methods_to_try:
            result = await self._try_http_request(url, method)

            if result["success"]:
                # 请求成功，存储到缓存并返回
                self._store_in_cache(url, result["data"])

                return {
                    "success": True,
                    "data": result["data"],
                    "source": "network",
                    "url": url,
                    "method": result["method"],
                    "status_code": result["status_code"]
                }
            else:
                # 请求失败，记录错误并尝试下一个方法
                last_error = result
                debug_print(f"{method} request failed for {url}: {result.get('error', 'Unknown error')}")

        # 所有方法都失败，返回最后一个错误
        debug_print(f"All HTTP methods failed for {url}")
        return last_error

    async def fetch_coding_standards(self, url: str, domain: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        异步从URL获取编码规范文本内容

        Args:
            url: 要获取编码规范的URL
            domain: 编码领域描述（编程语言、框架、技术等）
            force_refresh: 是否强制刷新缓存

        Returns:
            包含状态和编码规范内容的字典
        """
        debug_print(f"Fetching coding standards from URL: {url}, domain: {domain}, force_refresh: {force_refresh}")

        # 清理过期缓存
        self._cleanup_expired_cache()

        # 使用URL和domain组合作为缓存键，添加前缀避免与JSON缓存冲突
        cache_key = f"coding_standards:{url}:{hash(domain)}"

        # 检查缓存（除非强制刷新）
        if not force_refresh:
            cached_content = self._get_from_cache(cache_key)
            if cached_content is not None:
                return {
                    "success": True,
                    "content": cached_content,
                    "domain": domain,
                    "source": "cache",
                    "url": url
                }

        # 尝试HTTP请求：先GET，后POST
        methods_to_try = ['GET', 'POST']
        last_error = None

        for method in methods_to_try:
            result = await self._try_http_request_text(url, method)

            if result["success"]:
                # 请求成功，存储到缓存并返回
                self._store_in_cache(cache_key, result["content"])

                return {
                    "success": True,
                    "content": result["content"],
                    "domain": domain,
                    "source": "network",
                    "url": url,
                    "method": result["method"],
                    "status_code": result["status_code"]
                }
            else:
                # 请求失败，记录错误并尝试下一个方法
                last_error = result
                debug_print(f"{method} request failed for {url}: {result.get('error', 'Unknown error')}")

        # 所有方法都失败，返回最后一个错误
        debug_print(f"All HTTP methods failed for coding standards URL: {url}")
        return {
            "success": False,
            "error": last_error.get("error", "Unknown error"),
            "domain": domain,
            "url": url,
            "method": last_error.get("method", "Unknown"),
            "status_code": last_error.get("status_code")
        }

    def clear_cache(self) -> int:
        """清理所有缓存"""
        cache_size = len(self.cache)
        self.cache.clear()
        debug_print(f"Cleared {cache_size} cached entries")
        return cache_size

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        now = datetime.now()
        cache_info = {
            "total_entries": len(self.cache),
            "cache_duration_minutes": self.cache_duration.total_seconds() / 60,
            "entries": []
        }

        for url, (cached_time, _) in self.cache.items():
            age_minutes = (now - cached_time).total_seconds() / 60
            is_valid = age_minutes < self.cache_duration.total_seconds() / 60

            cache_info["entries"].append({
                "url": url,
                "cached_time": cached_time.isoformat(),
                "age_minutes": round(age_minutes, 2),
                "is_valid": is_valid
            })

        return cache_info

    def __del__(self):
        """清理资源 - 注意：在异步环境中应使用 async with 或手动调用 close()"""
        # 在异步环境中，__del__ 不能调用异步方法
        # 应该使用 async with 语句或手动调用 await close()
        pass


# Create MCP server with FastMCP 2.0 best practices
mcp = FastMCP(
    name="url_json_mcp",
    instructions="""
    这是一个URL内容获取MCP服务器，提供以下功能：

    1. JSON数据获取：从任何URL获取JSON格式数据
    2. 编码规范获取：从指定URL获取编码规范文本内容
    3. 智能缓存：自动缓存内容，避免重复请求
    4. 缓存管理：提供缓存清理和状态查看功能

    使用 fetch_json_from_url() 获取JSON数据
    使用 fetch_coding_standards() 获取编码规范
    使用 get_cache_info() 查看缓存状态
    使用 clear_cache() 清理缓存

    环境变量配置：
    - FASTMCP_CACHE_DURATION: 缓存持续时间（分钟，默认30）
    - FASTMCP_HTTP_TIMEOUT: HTTP请求超时时间（秒，默认30.0）
    - FASTMCP_USER_AGENT: HTTP请求User-Agent（默认URL-JSON-MCP/2.0）
    - FASTMCP_LOG_LEVEL: 日志级别（DEBUG/INFO/WARNING/ERROR，默认INFO）
    """,
    dependencies=["httpx>=0.28.1"],
    include_tags={"public", "api", "cache"},
    exclude_tags={"internal", "deprecated"},
    tool_serializer=custom_serializer
)

# 兼容性包装器，保持向后兼容
class URLContentManager:
    """同步包装器，用于向后兼容"""

    def __init__(self, cache_duration_minutes: int = 30):
        self._async_manager = AsyncURLContentManager(cache_duration_minutes)
        self._loop = None

    def _get_loop(self):
        """获取或创建事件循环"""
        try:
            return asyncio.get_running_loop()
        except RuntimeError:
            if self._loop is None or self._loop.is_closed():
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
            return self._loop

    def fetch_json(self, url: str, force_refresh: bool = False) -> Dict[str, Any]:
        """同步包装器"""
        loop = self._get_loop()
        return loop.run_until_complete(self._async_fetch_json(url, force_refresh))

    async def _async_fetch_json(self, url: str, force_refresh: bool = False) -> Dict[str, Any]:
        """异步实现"""
        async with self._async_manager as manager:
            return await manager.fetch_json(url, force_refresh)

    def fetch_coding_standards(self, url: str, domain: str, force_refresh: bool = False) -> Dict[str, Any]:
        """同步包装器"""
        loop = self._get_loop()
        return loop.run_until_complete(self._async_fetch_coding_standards(url, domain, force_refresh))

    async def _async_fetch_coding_standards(self, url: str, domain: str, force_refresh: bool = False) -> Dict[str, Any]:
        """异步实现"""
        async with self._async_manager as manager:
            return await manager.fetch_coding_standards(url, domain, force_refresh)

    def clear_cache(self) -> int:
        """清理缓存"""
        return self._async_manager.clear_cache()

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return self._async_manager.get_cache_info()

    def close(self):
        """关闭资源"""
        if self._loop and not self._loop.is_closed():
            self._loop.run_until_complete(self._async_manager.close())

# Global URL manager instance
url_manager = None


@mcp.tool(tags={"public", "api", "json"})
async def fetch_json_from_url(url: str, force_refresh: bool = False) -> str:
    """
    从指定URL获取JSON内容

    这个工具支持从任何HTTP端点获取JSON数据，具有智能缓存和错误重试机制。
    会先尝试GET请求，失败后自动尝试POST请求。

    Args:
        url: 要获取JSON内容的URL地址
        force_refresh: 是否强制刷新缓存，忽略现有缓存（默认：false）

    Returns:
        JSON格式的字符串，包含获取的数据或错误信息

    Examples:
        fetch_json_from_url("https://api.github.com/repos/microsoft/vscode")
        fetch_json_from_url("https://api.example.com/data", force_refresh=true)
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    # 使用异步管理器
    async with AsyncURLContentManager() as async_manager:
        result = await async_manager.fetch_json(url, force_refresh)

    return json.dumps(result, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "cache", "management"})
async def clear_cache() -> str:
    """
    清理所有缓存的URL内容

    这个工具会清除所有已缓存的URL内容，包括JSON数据和编码规范。
    清理后下次请求将重新从网络获取数据。

    Returns:
        包含清理结果的JSON字符串，包括清理的条目数量

    Examples:
        clear_cache()
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    cleared_count = url_manager.clear_cache()
    return json.dumps({
        "success": True,
        "cleared_entries": cleared_count,
        "message": f"Cleared {cleared_count} cached entries"
    }, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "cache", "info"})
async def get_cache_info() -> str:
    """
    获取当前缓存状态信息

    这个工具提供详细的缓存状态信息，包括缓存条目数量、
    每个条目的缓存时间、有效性等详细信息。

    Returns:
        包含缓存信息的JSON字符串，包括：
        - 总缓存条目数
        - 缓存持续时间设置
        - 每个缓存条目的详细信息

    Examples:
        get_cache_info()
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    cache_info = url_manager.get_cache_info()
    return json.dumps({
        "success": True,
        "cache_info": cache_info
    }, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "api", "standards", "text"})
async def fetch_coding_standards(url: str, domain: str, force_refresh: bool = False) -> str:
    """
    从指定URL获取编码规范内容

    这个工具专门用于获取编码规范、开发标准等文本内容。
    支持智能缓存，会先尝试GET请求，失败后自动尝试POST请求。

    Args:
        url: 编码规范的HTTP端点地址
        domain: 编码领域描述，包含编程语言、框架、技术等信息
        force_refresh: 是否强制刷新缓存，忽略现有缓存（默认：false）

    Returns:
        JSON格式的字符串，包含编码规范内容或错误信息

    Examples:
        fetch_coding_standards("https://example.com/python-standards.txt", "Python, Django, REST API")
        fetch_coding_standards("https://example.com/js-standards.md", "JavaScript, React, TypeScript", force_refresh=true)
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    # 使用异步管理器
    async with AsyncURLContentManager() as async_manager:
        result = await async_manager.fetch_coding_standards(url, domain, force_refresh)

    return json.dumps(result, ensure_ascii=False, indent=2)


def main():
    """主函数，启动URL JSON MCP服务器"""
    debug_print("Starting URL JSON MCP Server...")
    debug_print(f"Configuration: cache_duration={config.cache_duration_minutes}min, "
                f"timeout={config.http_timeout}s, log_level={config.log_level}")

    # Initialize URL manager with configuration
    global url_manager
    url_manager = URLContentManager(cache_duration_minutes=config.cache_duration_minutes)
    debug_print("URL manager initialized")

    # Run MCP server with environment variable support
    transport = os.getenv('FASTMCP_TRANSPORT', 'stdio')
    host = os.getenv('FASTMCP_HOST', '127.0.0.1')
    port = int(os.getenv('FASTMCP_PORT', '8080'))

    if transport == 'stdio':
        mcp.run(transport='stdio')
    elif transport == 'http':
        debug_print(f"Starting HTTP server on {host}:{port}")
        mcp.run(transport='http', host=host, port=port)
    else:
        debug_print(f"Unknown transport: {transport}, falling back to stdio")
        mcp.run(transport='stdio')


if __name__ == "__main__":
    main()